#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版：解压项目中的ZIP文件并将Excel文件统一存储到"财务"文件夹中
只需要Python标准库，无需额外依赖
"""

import os
import zipfile
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_and_organize_excel():
    """解压ZIP文件并整理Excel文件到财务文件夹"""
    
    # 设置路径
    workspace_dir = Path(".")
    finance_dir = workspace_dir / "财务"
    temp_dir = workspace_dir / "temp_extract"
    
    # Excel文件扩展名
    excel_extensions = {'.xlsx', '.xls', '.xlsm', '.csv'}
    
    # 创建目标文件夹
    finance_dir.mkdir(exist_ok=True)
    temp_dir.mkdir(exist_ok=True)
    
    logger.info("开始处理ZIP文件...")
    
    # 查找所有ZIP文件
    zip_files = list(workspace_dir.glob("*.zip"))
    
    if not zip_files:
        logger.info("未找到ZIP文件")
        return
    
    logger.info(f"找到 {len(zip_files)} 个ZIP文件")
    
    total_excel_files = 0
    successful_extractions = 0
    
    # 处理每个ZIP文件
    for zip_file in zip_files:
        logger.info(f"正在处理: {zip_file.name}")
        
        # 为每个ZIP文件创建单独的解压目录
        extract_to = temp_dir / zip_file.stem
        extract_to.mkdir(exist_ok=True)
        
        try:
            # 直接解压，不指定编码参数（兼容旧版本Python）
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            logger.info(f"成功解压: {zip_file.name}")
            successful_extractions += 1

        except zipfile.BadZipFile as e:
            logger.error(f"ZIP文件损坏: {zip_file.name}, 错误: {e}")
            continue
        except Exception as e:
            logger.error(f"解压失败: {zip_file.name}, 错误: {e}")
            continue
        
        # 查找解压后的Excel文件
        excel_files = []
        for root, dirs, files in os.walk(extract_to):
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix.lower() in excel_extensions:
                    excel_files.append(file_path)
        
        if excel_files:
            logger.info(f"在 {zip_file.name} 中找到 {len(excel_files)} 个Excel文件")
            
            # 复制Excel文件到财务文件夹
            for excel_file in excel_files:
                try:
                    # 生成目标文件名，避免重名
                    target_name = excel_file.name
                    target_path = finance_dir / target_name
                    
                    # 如果文件已存在，添加序号
                    counter = 1
                    while target_path.exists():
                        name_parts = excel_file.stem, counter, excel_file.suffix
                        target_name = f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                        target_path = finance_dir / target_name
                        counter += 1
                    
                    shutil.copy2(excel_file, target_path)
                    logger.info(f"复制Excel文件: {excel_file.name} -> {target_name}")
                    total_excel_files += 1
                    
                except Exception as e:
                    logger.error(f"复制Excel文件失败: {excel_file}, 错误: {e}")
        else:
            logger.info(f"在 {zip_file.name} 中未找到Excel文件")

    # 清理临时文件夹
    try:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            logger.info("清理临时文件夹完成")
    except Exception as e:
        logger.error(f"清理临时文件夹失败: {e}")
    
    # 输出统计信息
    logger.info("=" * 50)
    logger.info("处理完成!")
    logger.info(f"成功解压: {successful_extractions}/{len(zip_files)} 个ZIP文件")
    logger.info(f"总共复制: {total_excel_files} 个Excel文件到 '财务' 文件夹")
    logger.info("=" * 50)
    
    # 显示财务文件夹中的文件
    if finance_dir.exists():
        finance_files = list(finance_dir.iterdir())
        if finance_files:
            logger.info("财务文件夹中的文件:")
            for file in sorted(finance_files):
                if file.is_file():
                    logger.info(f"  - {file.name}")

def main():
    """主函数"""
    try:
        extract_and_organize_excel()
        print("\n脚本执行完成！请检查 '财务' 文件夹中的Excel文件。")
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"程序执行出错: {e}")

if __name__ == "__main__":
    main()
